# <PERSON>
library(optparse)
library(reshape2)
library(ComplexUpset)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load data and boolean
input_df = read.csv(opt$figure_data_file, sep='\t')



upset_plot = function(fc_cutoff, df){

# subset to df
input_df = df[df$fc_cutoff == fc_cutoff,]
input_df$fc_cutoff = NULL
rownames(input_df) = input_df$X
input_df$X = NULL

# create boolean mask
input_df = input_df < 0.05

# drop rows with only FALSE
input_df = input_df[rowSums(input_df) > 0,]

# keep as a data frame
input_df = as.data.frame(input_df)

# create plot
upset(input_df, colnames(input_df), name = paste0("Differentially expressed genes FC > ", fc_cutoff), wrap=TRUE)
}




pdf(opt$figure_file, width = 10, height = 5)


# create plot for each fc cutoff
lapply(unique(input_df$fc_cutoff), upset_plot, input_df)


dev.off()

print(opt$figure_file)


