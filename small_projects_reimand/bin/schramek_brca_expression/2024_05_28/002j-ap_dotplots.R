# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(data.table)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--enriched_pathways_dotplot_file"), type="character", default=NULL,
            help="",
            dest="enriched_pathways_dotplot_file"),
    make_option(c("-b","--gene_contributions_file"), type="character", default=NULL,
            help="",
            dest="gene_contributions_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



if(grepl("PLGRKT", opt$figure_file)) {
pval_limit = 3.5
fc_limit = 2
}else{
pval_limit = 10
fc_limit = 5
}

do_plot = function(pathway, genes_df, pathways_df){

sub_gene_df = genes_df[genes_df$pathway == pathway,]
sub_gene_df$gene = fct_reorder(sub_gene_df$gene, sub_gene_df$plot_pval, .desc=TRUE)

sub_pathways_df = pathways_df[pathways_df$pathway == pathway,]


p = ggplot(data=sub_gene_df, aes(x = gene, y = source, fill = logFC, size = plot_pval)) + plot_theme() +
geom_point(pch=21) +

scale_fill_gradient2(low = 'darkcyan', mid = 'white', high='darkorange', limits = c(lower_fc, upper_fc)) +
scale_size_continuous(limits=c(0,pval_limit)) +

ggtitle(sub_pathways_df$pathway_and_pval) + ylab('') + xlab('') +


guides(fill = guide_colourbar(title="FC (log2)", ticks.colour = "black", frame.colour = 'black'),
      size = guide_legend(title = "P-adjusted \n(-log10)"),
      colour = guide_legend(title = "Significant \n(padj < 0.05)")) +


theme(strip.placement = "outside", strip.background = element_blank(),
    legend.position="right")

print(p)

return()

}



pdf(opt$figure_file, width=18, height = 5)


# loads dfs
pathways_df = read.csv(opt$enriched_pathways_dotplot_file, sep='\t')
genes_df = read.csv(opt$gene_contributions_file, sep='\t')

upper_fc = fc_limit
lower_fc = -fc_limit

# subset to defining genes and set fc limits
genes_df$plot_pval[genes_df$plot_pval > pval_limit] = pval_limit
genes_df$logFC[genes_df$logFC > upper_fc] = upper_fc
genes_df$logFC[genes_df$logFC < lower_fc] = lower_fc

# order factors
genes_df$gene = factor(genes_df$gene, levels = unique(genes_df$gene))
pathways_df$pathway = fct_reorder(pathways_df$pathway, pathways_df$adjusted_p_val, .desc=FALSE)

lapply(levels(pathways_df$pathway), do_plot, genes_df, pathways_df)

dev.off()


print(opt$figure_file)




