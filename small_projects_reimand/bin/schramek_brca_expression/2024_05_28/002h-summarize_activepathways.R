# <PERSON>
library(optparse)
library(ComplexUpset)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load data and boolean
input_df = read.csv(opt$figure_data_file, sep='\t', row.names=1)
input_df = input_df < 0.05

# keep as a data frame
input_df = as.data.frame(input_df)

pdf(opt$figure_file, width = 5, height = 5)

# create plot
upset(input_df, colnames(input_df), name = "Differentially expressed pathways", width_ratio=0.1, wrap=TRUE)


dev.off()

print(opt$figure_file)


