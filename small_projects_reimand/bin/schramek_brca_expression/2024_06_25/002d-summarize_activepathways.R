# <PERSON>
library(optparse)
library(ComplexUpset)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load data and boolean
input_df = read.csv(opt$figure_data_file, sep='\t', row.names=1)
input_df = input_df < 0.05

# keep as a data frame
input_df = as.data.frame(input_df)



pdf(opt$figure_file, width = 7, height = 5)

dgea_cutoffs = c('vs75', 'vs25')

for (dgea_cutoff in dgea_cutoffs){

sub_df = input_df[,grepl(dgea_cutoff, colnames(input_df))]
sub_df <- sub_df[rowSums(sub_df) > 0,]

# rename columns
new_colnames <- colnames(sub_df)
for (i in seq_along(new_colnames)) {
  if (grepl("PLGRKT|MRPS18B", new_colnames[i])) {
    new_colnames[i] <- sub("q", "upper_q", new_colnames[i])
  } else {
    new_colnames[i] <- sub("q", "lower_q", new_colnames[i])
  }
}

colnames(sub_df) <- new_colnames


# create plot
print(upset(sub_df, colnames(sub_df), name = "Enriched pathways", width_ratio=0.2, wrap=TRUE))

}



dev.off()

print(opt$figure_file)


