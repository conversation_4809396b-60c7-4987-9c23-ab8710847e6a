# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess, re

help_message = '''
Failed
'''

def summarize_ap_results(ap_results_files_csv):
    # combined df
    ap_df = []

    # for each ap result file, load and summarize
    for i, file in enumerate(ap_results_files_csv.split(",")):
        df = pd.read_csv(file)
        df.index = df.iloc[:,0].to_numpy()

        # subset to the P-value to combine columns between dfs
        df = df.loc[:,['adjusted_p_val']]

        # add source name
        source_name = file.split("/")[-1].split("-")[0].split(".")[0]
        source_name = re.sub("_Basal", "", source_name)

        df.columns = [source_name]

        # append to ap_df
        ap_df.append(df)

    ap_df = pd.concat(ap_df, axis = 1)
    ap_df = ap_df.fillna(1)

    return ap_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process ap results for summarizing number of pathways
    res_df = summarize_ap_results(ap_results_files_csv)

    # save results
    res_df.to_csv(figure_data_file, sep="\t")

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["ap_results_files_csv=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--ap_results_files_csv"):
            ap_results_files_csv = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




