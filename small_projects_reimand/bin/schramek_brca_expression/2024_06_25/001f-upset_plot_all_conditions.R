# <PERSON>
library(optparse)
library(reshape2)
library(ComplexUpset)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load data and boolean
input_df = read.csv(opt$figure_data_file, sep='\t')



upset_plot = function(fc_cutoff, df){

# subset to df
input_df = df[df$fc_cutoff == fc_cutoff,]
input_df$fc_cutoff = NULL
rownames(input_df) = input_df$X
input_df$X = NULL

# create boolean mask
input_df = input_df < 0.05

# drop rows with only FALSE
input_df = input_df[rowSums(input_df) > 0,]

# keep as a data frame
input_df = as.data.frame(input_df)

print(head(input_df))

    
# rename columns
new_colnames <- colnames(input_df)
for (i in seq_along(new_colnames)) {
  if (grepl("PLGRKT", new_colnames[i])) {
    new_colnames[i] <- sub(".q", "-upper_q", new_colnames[i])
  } else {
    new_colnames[i] <- sub(".q", "-lower_q", new_colnames[i])
  }
}

colnames(input_df) <- new_colnames


# create plot
print(upset(input_df, colnames(input_df), name = paste0("Differentially expressed genes FC > ", 2**fc_cutoff), wrap=TRUE, width_ratio=0.2))

}




pdf(opt$figure_file, width = 10, height = 5)

dgea_cutoffs = c('vs75', 'vs25')

for (dgea_cutoff in dgea_cutoffs){

sub_df = input_df[,grepl(dgea_cutoff, colnames(input_df))]
sub_df$fc_cutoff = input_df$fc_cutoff
sub_df$X = input_df$X
    
# create plot for each fc cutoff
lapply(unique(sub_df$fc_cutoff), upset_plot, sub_df)

}


dev.off()

print(opt$figure_file)


