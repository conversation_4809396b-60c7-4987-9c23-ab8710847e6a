# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess, glob

help_message = '''
Failed
'''


def load_and_combine_dfs(files):
    res_df = pd.DataFrame()

    # iteratively load and combine dfs
    for file in files:
        df = pd.read_csv(file, sep='\t')

        # add description
        df['gene'] = file.split('/')[-1].split('_')[0].upper()
        df['source'] = 'mouse' if 'mouse' in file else 'human'

        res_df = pd.concat([res_df, df])

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    

    # list files
    files = glob.glob(os.path.join(mouse_pathway_path, "*_mouse_cytoscape-pathways.txt"))
    files.extend(glob.glob(os.path.join(human_pathway_path, "*_Basal_q25vs25-pathways.txt")))

    # combine enriched pathways
    res_df = load_and_combine_dfs(files)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mouse_pathway_path=", "human_pathway_path=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print(help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mouse_pathway_path"):
            mouse_pathway_path = str(arg)
        if opt in ("--human_pathway_path"):
            human_pathway_path = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


