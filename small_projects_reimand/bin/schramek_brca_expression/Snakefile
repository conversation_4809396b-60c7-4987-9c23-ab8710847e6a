# Alec Bahcheli

# run version (typically date)
VERSION='schramek_brca_expression/2025_06_17'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])


# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", VERSION, "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", VERSION, "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'




subtypes_csv = 'Basal,Claudin-low,HER2-Positive,Luminal_A,Luminal_B,Normal-like'
subtype_list = subtypes_csv.split(",")

genes_csv = 'PLGRKT,METTL14,FBXW7,CSNK1A1,MRPS18B'
gene_list = genes_csv.split(",")

dichot_methods_csv = 'q25vs25'
dichot_methods_list = dichot_methods_csv.split(",")


# mouse_genes_csv = 'Csnk1a1,Fbxw7,Mettl14,Plgrkt'
# mouse_gene_list = mouse_genes_csv.split(",")


###################################
# Complete workflow
###################################

# define the objective (make the output files)
rule all:
    input:
        # expression comparisons
        FIGURE_DIR + "/001-gene_expression_boxplots.pdf",
        FIGURE_DIR + "/001-volcano_plots.pdf",

        FIGURE_DIR + "/001-upset_plot_all_conditions.pdf",

        FIGURE_DIR + "/002-summarize_activepathways_single_gene.pdf",
        FIGURE_DIR + "/002-pathway_upset_plots.pdf",

        # expand("{figure_dir}/002-pathway_genes-{gene}_{subtype}_{method}.pdf", figure_dir = FIGURE_DIR, gene = gene_list, subtype = ['Basal'], method = dichot_methods_list),

        expand("{res_dir}/combined_ap_q25vs25/002-combined_enrichment_file.tsv", res_dir = RES_DIR)


# create main project directories
rule main_directories:        
    output:
        data = DATA_DIR + "/null.txt",
        raw_data = RAW_DATA_DIR + "/null.txt",
        ref_data = REF_DATA_DIR + "/null.txt",
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR} {DATA_DIR} {RAW_DATA_DIR} {REF_DATA_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures} {output.data} {output.raw_data} {output.ref_data}")




#####################
# gprofiler analysis 
#####################
# include: "snakemake/001-dgea_pathways.smk"
# include: "snakemake/002-mouse_analysis.smk"
include: "snakemake/003-dgea_pathways_revisions.smk"


