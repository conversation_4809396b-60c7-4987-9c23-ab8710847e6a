# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# TCGA expression and DGEA
#####################

rule preprocess_visualize_expression:
    input:
        FIGURE_DATA_DIR + "/null.txt",

        tpm_expression_file = RAW_DATA_DIR + "/TCGA_TPM_TCGA-BRCA.csv",
        counts_expression_file = RAW_DATA_DIR + "/TCGA_rawcounts_TCGA-BRCA-counts.csv",
        
        subtype_file = REF_DATA_DIR + "/PAM50_clusters.tsv",

        script = BIN_DIR + "/001a-gene_expression_boxplots.py",
        r_script = BIN_DIR + "/001b-gene_expression_boxplots.R"
        
    output:
        tpm_outfile = DATA_DIR + "/brca_tpm.tsv",
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        figure_data_file = FIGURE_DATA_DIR + "/001-gene_expression_boxplots.tsv",
        figure_file = FIGURE_DIR + "/001-gene_expression_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_expression_file {input.tpm_expression_file} --counts_expression_file {input.counts_expression_file} --subtype_file {input.subtype_file} --r_script {input.r_script} --tpm_outfile {output.tpm_outfile} --counts_outfile {output.counts_outfile} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


rule dgea_analysis_quartile:
    input:
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        r_script = BIN_DIR + "/001c-dgea_edger_quartile.R"
        
    output:
        dgea_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_q25vs75.tsv"

    params:
        subtype = '{subtype}',
        gene = '{gene}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/dgea/bin/Rscript {input.r_script} --counts_outfile {input.counts_outfile} --subtype {params.subtype} --gene {params.gene} --dgea_file {output.dgea_file}"


rule dgea_analysis_quartiles_only:
    input:
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        r_script = BIN_DIR + "/001c-dgea_edger_quartiles_only.R"
        
    output:
        dgea_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_q25vs25.tsv"

    params:
        subtype = '{subtype}',
        gene = '{gene}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/dgea/bin/Rscript {input.r_script} --counts_outfile {input.counts_outfile} --subtype {params.subtype} --gene {params.gene} --dgea_file {output.dgea_file}"



rule volcano_plots:
    input:
        expand("{res_dir}/dgea/001-dgea_{subtype}_{gene}_{method}.tsv", res_dir = RES_DIR, subtype = subtype_list, gene = gene_list, method = dichot_methods_list),

        script = BIN_DIR + "/001d-volcano_plots.py",
        r_script = BIN_DIR + "/001e-volcano_plots.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",
        figure_file = FIGURE_DIR + "/001-volcano_plots.pdf"

    params:
        dgea_dir = RES_DIR + "/dgea/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dgea_dir {params.dgea_dir} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


rule venn_diagrams:
    input:
        dgea_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",

        r_script = BIN_DIR + "/001f-venn_diagrams.R"
        
    output:
        figure_file = FIGURE_DIR + "/001-venn_diagrams.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --dgea_file {input.dgea_file} --figure_file {output.figure_file}"


rule venn_diagrams_all_conditions:
    input:
        dgea_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",

        r_script = BIN_DIR + "/001f-venn_diagrams_all_conditions.R"
        
    output:
        figure_file = FIGURE_DIR + "/001-venn_diagrams_all_conditions.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --dgea_file {input.dgea_file} --figure_file {output.figure_file}"


rule upset_plot_all_conditions:
    input:
        dgea_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",

        script = BIN_DIR + "/001f-upset_plot_all_conditions.py",
        r_script = BIN_DIR + "/001f-upset_plot_all_conditions.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/001-upset_plot_all_conditions.tsv",
        figure_file = FIGURE_DIR + "/001-upset_plot_all_conditions.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --r_script {input.r_script} --dgea_file {input.dgea_file} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



#####################
# pathway enrichment
#####################


# single pathway enrichment
rule ActivePathways_single_gene:
    input:
        # expand("{res_dir}/ap/{{subtype}}_{gene}_{{method}}.csv", res_dir = RES_DIR, gene = gene_list),
        input_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_{method}.tsv",

        gmt_file = DATA_DIR + "/ref_data/hsapiens_gobp_reactome_2024_05_17.gmt",

        script = BIN_DIR + "/002a-activepathways.py",
        r_script = BIN_DIR + "/002b-activepathways.R"
        
    output:
        ap_input_file = RES_DIR + "/ap_{cutoff}/input-{gene}_{subtype}_{method}.tsv",
        pathways_outfile = RES_DIR + "/ap_{cutoff}/{gene}_{subtype}_{method}-pathways.txt",

        output_file = RES_DIR + "/ap_{cutoff}/{gene}_{subtype}_{method}.csv"

    params:
        cytoscape_file_prefix = RES_DIR + '/ap_{cutoff}/{gene}_{subtype}_{method}-',
        max_min_genes = '750,50',
        subtype = '{subtype}',
        dichot_method = '{method}',
        cutoff = '{cutoff}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_file {input.input_file} --subtype {params.subtype} --dichot_method {params.dichot_method} --r_script {input.r_script} --ap_input_file {output.ap_input_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --output_file {output.output_file} --max_min_genes {params.max_min_genes} --fc_cutoff {params.cutoff}"



rule summarize_ap_single_gene:
    input:
        expand("{res_dir}/ap/{gene}_{subtype}_{method}.csv", res_dir = RES_DIR, gene = gene_list, subtype = ['Basal'], method = dichot_methods_list),

        script = BIN_DIR + "/002c-summarize_activepathways.py",
        r_script = BIN_DIR + "/002d-summarize_activepathways.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/002-summarize_activepathways_single_gene.tsv",
        figure_file = FIGURE_DIR + "/002-summarize_activepathways_single_gene.pdf"

    params:
        ap_results_files_csv = ",".join(expand("{res_dir}/ap/{gene}_{subtype}_{method}.csv", res_dir = RES_DIR, gene = gene_list, subtype = ['Basal'], method = dichot_methods_list))

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --ap_results_files_csv {params.ap_results_files_csv} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



rule pathway_gene_contributions:
    input:
        dgea_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_{method}.tsv",
        contributions_details_file = RES_DIR + "/ap/{gene}_{subtype}_{method}.csv",
        pathways_outfile = RES_DIR + "/ap/{gene}_{subtype}_{method}-pathways.txt",

        script = BIN_DIR + "/002e-ap_dotplots.py",
        r_script = BIN_DIR + "/002f-ap_dotplots.R"
        
    output:
        enriched_pathway_gene_contributions_file = FIGURE_DATA_DIR + "/002-pathway_gene_contributions-{gene}_{subtype}_{method}.tsv",
        gene_contributions_file = FIGURE_DATA_DIR + "/002-gene_cotnributions-{gene}_{subtype}_{method}.tsv",

        figure_file = FIGURE_DIR + "/002-pathway_genes-{gene}_{subtype}_{method}.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dgea_file {input.dgea_file} --contributions_details_file {input.contributions_details_file} --pathways_outfile {input.pathways_outfile} --r_script {input.r_script} --enriched_pathway_gene_contributions_file {output.enriched_pathway_gene_contributions_file} --gene_contributions_file {output.gene_contributions_file} --figure_file {output.figure_file}"




rule combined_enrichment_map:
    input:
        expand("{res_dir}/ap_{{cutoff}}/{gene}_{subtype}_{method}.csv", res_dir = RES_DIR, gene = gene_list, subtype = ['Basal'], method = dichot_methods_list),

        gmt_file = DATA_DIR + "/ref_data/hsapiens_gobp_reactome_2024_05_17.gmt",

        script = BIN_DIR + "/002g-combined_enrichment_map.py"

    output:
        combined_pathway_enrichment_file = RES_DIR + "/combined_ap_{method}_{cutoff}/002-combined_enrichment_file.tsv",
        cytoscape_instructions_file = RES_DIR + "/combined_ap_{method}_{cutoff}/002-combined_cytoscape_instructions_file.tsv",
        merged_gmt_file = RES_DIR + "/combined_ap_{method}_{cutoff}/002-merged_gmt_file.gmt"

    params:
        ap_results_files_csv = ",".join(expand("{res_dir}/ap_{{cutoff}}/{gene}_{subtype}_{{method}}.csv", res_dir = RES_DIR, gene = gene_list, subtype = ['Basal']))

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --ap_results_files_csv {params.ap_results_files_csv} --original_gmt_file {input.gmt_file} --combined_pathway_enrichment_file {output.combined_pathway_enrichment_file} --cytoscape_instructions_file {output.cytoscape_instructions_file} --merged_gmt_file {output.merged_gmt_file}"







