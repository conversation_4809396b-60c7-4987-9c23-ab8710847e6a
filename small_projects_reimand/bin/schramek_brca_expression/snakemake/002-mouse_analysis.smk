# Alec <PERSON>cheli - <EMAIL>


#####################
# DGEA
#####################

rule mouse_volcano_plots:
    input:
        expand("{data_dir}/mouse_data/sg{gene}_allgenes.csv", data_dir = DATA_DIR, gene = mouse_gene_list),

        script = BIN_DIR + "/005a-volcano_plots.py",
        r_script = BIN_DIR + "/005b-volcano_plots.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/005-mouse_dgea_combined.tsv",
        figure_file = FIGURE_DIR + "/005-mouse_volcano_plots.pdf"

    params:
        dgea_dir = DATA_DIR + "/mouse_data/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dgea_dir {params.dgea_dir} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


#####################
# pathway enrichment
#####################

# single pathway enrichment
rule mouse_ActivePathways:
    input:
        input_file = DATA_DIR + "/mouse_data/sg{gene}_allgenes.csv",

        gmt_file = DATA_DIR + "/ref_data/gprofiler_mmusculus_gobp_reactome-26_06_2024.gmt",
        mouse_genes_bed_file = DATA_DIR + "/mouse_data/MM39_genes.bed",

        script = BIN_DIR + "/003a-mouse_activepathways.py",
        r_script = BIN_DIR + "/003b-mouse_activepathways.R"
        
    output:
        ap_input_file = RES_DIR + "/ap_mouse/input-{gene}_mouse.tsv",
        pathways_outfile = RES_DIR + "/ap_mouse/{gene}_mouse_cytoscape-pathways.txt",

        output_file = RES_DIR + "/ap_mouse/{gene}_mouse.csv"

    params:
        cytoscape_file_prefix = RES_DIR + '/ap_mouse/{gene}_mouse_cytoscape-',
        max_min_genes = '750,50'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --input_file {input.input_file} --mouse_genes_bed_file {input.mouse_genes_bed_file} --r_script {input.r_script} --ap_input_file {output.ap_input_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --output_file {output.output_file} --max_min_genes {params.max_min_genes}"



rule mouse_summarize_ap_single_gene:
    input:
        expand("{res_dir}/ap_mouse/{gene}_mouse.csv", res_dir = RES_DIR, gene = mouse_gene_list),

        script = BIN_DIR + "/003c-mouse_summarize_activepathways.py",
        r_script = BIN_DIR + "/003d-mouse_summarize_activepathways.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/003-mouse_summarize_activepathways_single_gene.tsv",
        figure_file = FIGURE_DIR + "/003-mouse_summarize_activepathways_single_gene.pdf"

    params:
        ap_results_files_csv = ",".join(expand("{res_dir}/ap_mouse/{gene}_mouse.csv", res_dir = RES_DIR, gene = mouse_gene_list))

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --ap_results_files_csv {params.ap_results_files_csv} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



rule mouse_pathway_gene_contributions:
    input:
        dgea_file = DATA_DIR + "/mouse_data/sg{gene}_allgenes.csv",
        contributions_details_file = RES_DIR + "/ap_mouse/{gene}_mouse.csv",
        pathways_outfile = RES_DIR + "/ap_mouse/{gene}_mouse_cytoscape-pathways.txt",

        script = BIN_DIR + "/003e-mouse_ap_dotplots.py",
        r_script = BIN_DIR + "/003f-mouse_ap_dotplots.R"
        
    output:
        enriched_pathway_gene_contributions_file = FIGURE_DATA_DIR + "/003-mouse_pathway_gene_contributions-{gene}.tsv",
        gene_contributions_file = FIGURE_DATA_DIR + "/003-mouse_gene_contributions-{gene}.tsv",

        figure_file = FIGURE_DIR + "/003-mouse_pathway_genes-{gene}.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --dgea_file {input.dgea_file} --contributions_details_file {input.contributions_details_file} --pathways_outfile {input.pathways_outfile} --r_script {input.r_script} --enriched_pathway_gene_contributions_file {output.enriched_pathway_gene_contributions_file} --gene_contributions_file {output.gene_contributions_file} --figure_file {output.figure_file}"



rule mouse_combined_enrichment_map:
    input:
        expand("{res_dir}/ap_mouse/{gene}_mouse.csv", res_dir = RES_DIR, gene = mouse_gene_list),

        gmt_file = DATA_DIR + "/ref_data/gprofiler_mmusculus_gobp_reactome-26_06_2024.gmt",

        script = BIN_DIR + "/003g-combined_enrichment_map.py"

    output:
        combined_pathway_enrichment_file = RES_DIR + "/combined_ap_mouse/003-mouse_combined_enrichment_file.tsv",
        cytoscape_instructions_file = RES_DIR + "/combined_ap_mouse/003-mouse_combined_cytoscape_instructions_file.tsv",
        merged_gmt_file = RES_DIR + "/combined_ap_mouse/003-mouse_merged_gmt_file.gmt"

    params:
        ap_results_files_csv = ",".join(expand("{res_dir}/ap_mouse/{gene}_mouse.csv", res_dir = RES_DIR, gene = mouse_gene_list))

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --ap_results_files_csv {params.ap_results_files_csv} --original_gmt_file {input.gmt_file} --combined_pathway_enrichment_file {output.combined_pathway_enrichment_file} --cytoscape_instructions_file {output.cytoscape_instructions_file} --merged_gmt_file {output.merged_gmt_file}"



#####################
# comparing human vs. mouse
#####################


rule mouse_human_pathway_venn_diagrams:
    input:
        expand("{res_dir}/ap_mouse/{gene}_mouse_cytoscape-pathways.txt", res_dir = RES_DIR, gene = mouse_gene_list),
        expand("{res_dir}/ap/{gene}_Basal_q25vs25-pathways.txt", res_dir = RES_DIR, gene = gene_list),

        script = BIN_DIR + "/004a-mouse_human_pathway_venn_diagrams.py",
        r_script = BIN_DIR + "/004b-mouse_human_pathway_venn_diagrams.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/004-mouse_human_pathway_venn_diagrams.tsv",
        figure_file = FIGURE_DIR + "/004-mouse_human_pathway_venn_diagrams.pdf"

    params:
        mouse_pathway_path = RES_DIR + "/ap_mouse/",
        human_pathway_path = RES_DIR + "/ap/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --mouse_pathway_path {params.mouse_pathway_path} --human_pathway_path {params.human_pathway_path} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"










