# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# TCGA expression and DGEA
#####################

rule preprocess_visualize_expression:
    input:
        FIGURE_DATA_DIR + "/null.txt",

        tpm_expression_file = RAW_DATA_DIR + "/TCGA_TPM_TCGA-BRCA.csv",
        counts_expression_file = RAW_DATA_DIR + "/TCGA_rawcounts_TCGA-BRCA-counts.csv",
        
        subtype_file = REF_DATA_DIR + "/PAM50_clusters.tsv",

        script = BIN_DIR + "/001a-gene_expression_boxplots.py",
        r_script = BIN_DIR + "/001b-gene_expression_boxplots.R"
        
    output:
        tpm_outfile = DATA_DIR + "/brca_tpm.tsv",
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        figure_data_file = FIGURE_DATA_DIR + "/001-gene_expression_boxplots.tsv",
        figure_file = FIGURE_DIR + "/001-gene_expression_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_expression_file {input.tpm_expression_file} --counts_expression_file {input.counts_expression_file} --subtype_file {input.subtype_file} --r_script {input.r_script} --tpm_outfile {output.tpm_outfile} --counts_outfile {output.counts_outfile} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


rule dgea_analysis_median:
    input:
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        r_script = BIN_DIR + "/001c-dgea_edger.R"
        
    output:
        dgea_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_median.tsv"

    params:
        subtype = '{subtype}',
        gene = '{gene}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --counts_outfile {input.counts_outfile} --subtype {params.subtype} --gene {params.gene} --dgea_file {output.dgea_file}"

rule dgea_analysis_quartile:
    input:
        counts_outfile = DATA_DIR + "/brca_counts.tsv",

        r_script = BIN_DIR + "/001c-dgea_edger_quartile.R"
        
    output:
        dgea_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_quartile.tsv"

    params:
        subtype = '{subtype}',
        gene = '{gene}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --counts_outfile {input.counts_outfile} --subtype {params.subtype} --gene {params.gene} --dgea_file {output.dgea_file}"



rule volcano_plots:
    input:
        expand("{res_dir}/dgea/001-dgea_{subtype}_{gene}_{method}.tsv", res_dir = RES_DIR, subtype = subtype_list, gene = gene_list, method = dichot_methods_list),

        script = BIN_DIR + "/001d-volcano_plots.py",
        r_script = BIN_DIR + "/001e-volcano_plots.R"
        
    output:
        figure_data_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",
        figure_file = FIGURE_DIR + "/001-volcano_plots.pdf"

    params:
        dgea_dir = RES_DIR + "/dgea/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dgea_dir {params.dgea_dir} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


rule venn_diagrams:
    input:
        dgea_file = FIGURE_DATA_DIR + "/001-dgea_combined.tsv",

        r_script = BIN_DIR + "/001f-venn_diagrams.R"
        
    output:
        figure_file = FIGURE_DIR + "/001-venn_diagrams.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --dgea_file {input.dgea_file} --figure_file {output.figure_file}"





#####################
# pathway enrichment
#####################

# subset to primary and metastatic
rule ActivePathways:
    input:
        # expand("{res_dir}/ap/{{subtype}}_{gene}_{{method}}.csv", res_dir = RES_DIR, gene = gene_list),
        # scores_file = RES_DIR + "/dgea/001-dgea_{subtype}_{gene}_{method}.tsv",
        gmt_file = DATA_DIR + "/ref_data/hsapiens_gobp_reactome_2024_05_17.gmt",

        script = BIN_DIR + "/002a-activepathways.py",
        r_script = BIN_DIR + "/002b-activepathways.R"
        
    output:
        ap_input_file = RES_DIR + "/ap/input-{subtype}_{method}.tsv",
        output_file = RES_DIR + "/ap/{subtype}_{method}.csv"

    params:
        scores_file_path = RES_DIR + '/dgea/',
        cytoscape_file_prefix = RES_DIR + '/ap/{subtype}_{method}-',
        max_min_genes = '750,10',
        subtype = '{subtype}',
        dichot_method = '{method}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --scores_file_path {params.scores_file_path} --subtype {params.subtype} --dichot_method {params.dichot_method} --r_script {input.r_script} --ap_input_file {output.ap_input_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --output_file {output.output_file} --max_min_genes {params.max_min_genes}"













