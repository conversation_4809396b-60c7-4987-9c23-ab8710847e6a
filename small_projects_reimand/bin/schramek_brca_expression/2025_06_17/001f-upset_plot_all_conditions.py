# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''


def process_df(dgea_file, subtype = 'Basal', fc_cutoffs = [1, 0.5849625007211562]):
    # load the file
    df = pd.read_csv(dgea_file, sep='\t')
    df = df[df['subtype'] == subtype]
    df.index = df['gene'].to_numpy()

    res_df = []

    # for each fc cutoff, get the p-values for each dichot_method
    for fc_cutoff in fc_cutoffs:
        # subset based on fc cutoff
        sub_df = df[df['logFC'] >= fc_cutoff]

        tmp_res_df = []
        for dichot_method in sub_df['dichot_method'].unique():
            for gene in sub_df['dgea_gene'].unique():
                # mask the gene
                mask = np.logical_and(sub_df['dgea_gene'] == gene, sub_df['dichot_method'] == dichot_method)

                # subset to p-values
                tmp_df = sub_df.loc[mask,['adj.P.Val']]
                tmp_df.columns = [f"{gene}-{dichot_method}"]

                # append to df list
                tmp_res_df.append(tmp_df[~tmp_df.index.duplicated()])

        # concat the list of dfs
        tmp_res_df = pd.concat(tmp_res_df, axis=1)
        tmp_res_df['fc_cutoff'] = fc_cutoff
        tmp_res_df = tmp_res_df.fillna(1)

        res_df.append(tmp_res_df)

    # concat the list of dfs
    res_df = pd.concat(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs and process
    df = process_df(dgea_file)
    print(df)

    # save to file
    df.to_csv(figure_data_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_file"):
            dgea_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




