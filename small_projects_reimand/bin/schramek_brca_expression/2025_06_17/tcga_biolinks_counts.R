working_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/data/schramek_brca_expression/rawcounts/"

setwd(working_dir)


library(TCGAbiolinks)
library(SummarizedExperiment)


projects <- getGDCprojects()
project_ids <- projects$id[grepl("BRCA", projects$id)]


# for (project_id in project_ids[12:length(project_ids)]) {
for (project_id in project_ids) {
    query <- GDCquery(project = project_id,
                      data.category = "Transcriptome Profiling",
                      data.type = "Gene Expression Quantification",
                      workflow.type = "STAR - Counts")
    GDCdownload(query)
    data <- GDCprepare(query)

    expression_data <- assay(data, "stranded_first")
    gene_info <- as.data.frame(rowData(data))
    combined_data <- cbind(gene_info, expression_data)

    colnames(combined_data) <- c(colnames(gene_info), colnames(expression_data))
    
    filename <- paste0(working_dir, "TCGA_rawcounts_", project_id, "-counts.csv")
    write.csv(combined_data, filename, quote=FALSE)
}



