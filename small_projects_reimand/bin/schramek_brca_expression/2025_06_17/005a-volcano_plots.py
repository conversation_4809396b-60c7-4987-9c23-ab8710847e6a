# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''


def load_dgea_dfs(dgea_dir):
    # list all files with regex
    files = os.path.join(dgea_dir, "*_allgenes.csv")
    files = glob.glob(files)

    # load all files
    dfs = []

    for file in files:
        df = pd.read_csv(file, index_col=0)

        # make gene a column
        df['gene'] = df.index.to_numpy()

        # add details
        dgea_gene = file.split("/")[-1].split("_")[0].lstrip("sg")
        df['dgea_gene'] = dgea_gene

        dfs.append(df)

    # concat all dfs
    df = pd.concat(dfs)

    print(df.shape)
    # remove rows with na padj
    df = df.dropna(subset=['padj'])
    print(df.shape)
    
    return df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dgea dfs from dir
    df = load_dgea_dfs(dgea_dir)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_dir=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_dir"):
            dgea_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




