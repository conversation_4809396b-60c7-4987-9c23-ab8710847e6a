#!/usr/bin/env python3

import argparse
import pandas as pd
import os
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Create upset plots for pathway enrichment results')
    parser.add_argument('--pathway_files_csv', type=str, required=True, help='Comma-separated list of pathway files')
    parser.add_argument('--r_script', type=str, required=True, help='R script for generating upset plots')
    parser.add_argument('--figure_data_file', type=str, required=True, help='Output file for figure data')
    parser.add_argument('--figure_file', type=str, required=True, help='Output file for figure')
    
    args = parser.parse_args()
    
    # Split the comma-separated list of pathway files
    pathway_files = args.pathway_files_csv.split(',')
    
    # Create a dictionary to store pathway presence across files
    pathway_presence = {}
    
    # Process each pathway file
    for pathway_file in pathway_files:
        # Extract gene, subtype, and method from filename
        filename = os.path.basename(pathway_file)
        parts = filename.replace('-pathways.txt', '').split('_')
        
        if len(parts) >= 3:
            gene = parts[0]
            subtype = parts[1]
            method = parts[2]
            condition_name = f"{gene}_{subtype}_{method}"
            
            # Read the pathway file
            try:
                df = pd.read_csv(pathway_file, sep='\t')
                
                # Check if term_id column exists
                if 'term_id' in df.columns:
                    # Get unique pathway IDs
                    pathways = df['term_id'].unique()
                    
                    # Update pathway presence dictionary
                    for pathway in pathways:
                        if pathway not in pathway_presence:
                            pathway_presence[pathway] = {}
                        
                        pathway_presence[pathway][condition_name] = 1
            except Exception as e:
                print(f"Error processing {pathway_file}: {e}")
    
    # Create a DataFrame from the pathway presence dictionary
    presence_df = pd.DataFrame.from_dict(pathway_presence, orient='index')
    
    # Fill NaN values with 0
    presence_df = presence_df.fillna(0)
    
    # Save the DataFrame to the figure data file
    presence_df.to_csv(args.figure_data_file, sep='\t')
    
    # Call the R script to generate the upset plot
    cmd = [
        "Rscript",
        args.r_script,
        "--figure_data_file", args.figure_data_file,
        "--figure_file", args.figure_file
    ]
    
    subprocess.run(cmd, check=True)

if __name__ == "__main__":
    main()