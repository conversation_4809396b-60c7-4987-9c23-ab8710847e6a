#!/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript

library(optparse)
library(ComplexUpset)
library(dplyr)
library(ggplot2)

# set up the option parser
option_list = list(
  make_option(c("-d", "--figure_data_file"), type="character", default=NULL, 
              help="Input data file for the figure", metavar="character"),
  make_option(c("-f", "--figure_file"), type="character", default=NULL, 
              help="Output figure file", metavar="character")
)

parser <- OptionParser(usage = "%prog -d data.tsv -f figure.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

# load data
input_df = read.csv(opt$figure_data_file, sep='\t', row.names=1)

# Convert to binary matrix (0/1)
input_df = input_df > 0

# Keep as a data frame
input_df = as.data.frame(input_df)

# Rename columns to make them more readable
new_colnames <- colnames(input_df)
for (i in seq_along(new_colnames)) {
  parts <- strsplit(new_colnames[i], "_")[[1]]
  if (length(parts) >= 3) {
    gene <- parts[1]
    method <- parts[3]
    new_colnames[i] <- paste(gene, method, sep="_")
  }
}
colnames(input_df) <- new_colnames

# Create upset plot using ComplexUpset
upset_plot <- upset(
  input_df,
  intersect = colnames(input_df),
  name = "Enriched pathways",
  width_ratio = 0.2,
  wrap = TRUE,
  sort_sets = 'descending',
  sort_intersections = 'descending'
)

# Save to PDF
ggsave(opt$figure_file, upset_plot, width = 10, height = 7, device = "pdf")

print(paste("Figure saved to:", opt$figure_file))