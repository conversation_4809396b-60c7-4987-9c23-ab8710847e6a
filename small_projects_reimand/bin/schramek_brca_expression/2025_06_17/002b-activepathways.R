# <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--scores_file"), type="character", default=NULL,
            help="",
            dest="scores_file"),
    make_option(c("-b","--gmt_file"), type="character", default=NULL,
            help="",
            dest="gmt_file"),
    make_option(c("-c","--cytoscape_file_prefix"), type="character", default=NULL,
            help="",
            dest="cytoscape_file_prefix"),
    make_option(c("-d","--output_file"), type="character", default=NULL,
            help="",
            dest="output_file"),
    make_option(c("-e","--max_min_genes"), type="character", default=NULL,
            help="",
            dest="max_min_genes")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)





# load p-value df
df = read.csv(opt$scores_file, sep='\t', row.names=1)
scores = as.matrix(df)
colnames(scores) = colnames(df)

# set NAs to 1 and define colors
scores[is.na(scores)] = 1


# minimum and maximum number of genes
gene_limits = strsplit(opt$max_min_genes, ",")[[1]]
min_genes = as.numeric(gene_limits[2])
max_genes = as.numeric(gene_limits[1])


# run ActivePathways
enriched_pathways = ActivePathways(scores, opt$gmt_file, cytoscape_file_tag=opt$cytoscape_file_prefix, geneset_filter = c(min_genes, max_genes), background=rownames(scores))


# export enriched pathways
export_as_CSV(enriched_pathways, opt$output_file)

print("ActivePathways Complete")



