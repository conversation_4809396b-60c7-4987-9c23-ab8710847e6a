# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




barplot = function(source, input_df){

df = input_df[input_df$enrichment_details == source,]

    
ggplot(df, aes(x = gene_source, y = count)) + plot_theme() +

geom_bar(stat = "identity") +
geom_text(aes(label = count), vjust = -0.5, size = 3) +  # Add counts above bars

ggtitle(paste0('METTL14 nsig genes: ', unique(df$METTL14_sig), '; PLGRKT nsig genes: ', unique(df$PLGRKT_sig), collapse = '')) + 
    
ylab('N pathways') + xlab('') +

theme(plot.title = element_text(size = 12))


}






pdf(opt$figure_file)


# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# reorder factor levels
input_df$gene_source = fct_reorder(factor(input_df$gene_source), input_df$count, .fun = sum, .desc = TRUE)

# plot mutations for top GBM genes
lapply(unique(input_df$enrichment_details),barplot, input_df)

dev.off()


print(opt$figure_file)



