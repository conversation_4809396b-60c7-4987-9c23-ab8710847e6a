# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''

def load_dfs_for_ap(scores_file_path, subtype, dichot_method, fc_cutoff = 0.5849625007211562):
    # list of dfs
    dfs = []

    # regex pattern
    regex_pattern = os.path.join(scores_file_path, f'*{subtype}*{dichot_method}*')
    files = glob.glob(regex_pattern)


    # Iterate over files in the directory
    for i, file in enumerate(files):
        # Load the file as a DataFrame
        df = pd.read_csv(file, sep='\t', index_col=0)

        # Filter the DataFrame based on the fold change cutoff
        mask = df['logFC'].abs() < fc_cutoff
        df.loc[mask,'P.Value'] = 1
        
        # Add subtype and gene as columns
        gene = file.split("_")[-2]
        df[gene] = df['P.Value'].to_numpy()
        df = df.loc[:,[gene]]

        if i == 0:
            # Append the DataFrame to the list
            dfs = df.copy()
        else:
            dfs[gene] = df[gene]

    print(dfs)
    
    return dfs



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    df = load_dfs_for_ap(scores_file_path, subtype, dichot_method)

    print(df)

    # save to file
    df.to_csv(ap_input_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--scores_file', ap_input_file, '--gmt_file', gmt_file, '--cytoscape_file_prefix', cytoscape_file_prefix, '--output_file', output_file, '--max_min_genes', max_min_genes]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["scores_file_path=", "subtype=", "dichot_method=", "r_script=", "ap_input_file=", "gmt_file=", "cytoscape_file_prefix=", "output_file=", "max_min_genes="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--scores_file_path"):
            scores_file_path = str(arg)
        if opt in ("--subtype"):
            subtype = str(arg)
        if opt in ("--dichot_method"):
            dichot_method = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--ap_input_file"):
            ap_input_file = str(arg)
        if opt in ("--gmt_file"):
            gmt_file = str(arg)
        if opt in ("--cytoscape_file_prefix"):
            cytoscape_file_prefix = str(arg)
        if opt in ("--output_file"):
            output_file = str(arg)
        if opt in ("--max_min_genes"):
            max_min_genes = str(arg)

            
    main()




