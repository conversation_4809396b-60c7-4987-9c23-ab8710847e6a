# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''

def summarize_ap_results(ap_input_files_csv, ap_results_files_csv, sig_cutoff=0.05):
    # combined df
    ap_df = []

    # for each ap result file, load and summarize
    for i, file in enumerate(ap_results_files_csv.split(",")):
        df = pd.read_csv(file)

        # load gene df
        gene_df = pd.read_csv(ap_input_files_csv.split(",")[i], sep="\t", index_col=0)

        # calculate the number of pathways from each 
        df = pd.DataFrame(np.unique(df['evidence'], return_counts=True)).transpose()
        df.columns = ['gene_source', 'count']
        df['enrichment_details'] = file.split("/")[-1].split(".")[0]

        df['gene_source'] = df['gene_source'].str.replace("|", " and ")

        # add columns for the number of significant genes from each genes of gene_df
        for col in gene_df.columns:
            df[col + "_sig"] = np.sum(gene_df[col] < sig_cutoff)

        # append to ap_df
        ap_df.append(df)

    ap_df = pd.concat(ap_df)

    return ap_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process ap results for summarizing number of pathways
    res_df = summarize_ap_results(ap_input_files_csv, ap_results_files_csv)

    # save results
    res_df.to_csv(figure_data_file, index=False, sep="\t")

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["ap_input_files_csv=", "ap_results_files_csv=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--ap_input_files_csv"):
            ap_input_files_csv = str(arg)
        if opt in ("--ap_results_files_csv"):
            ap_results_files_csv = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




