# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import re, subprocess

help_message = '''
Failed
'''

def protein_coding_genes_arr(protein_coding_file = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hgnc_gene_annotations.tsv"):
    # load and subset to protein-coding
    pc = pd.read_csv(protein_coding_file, sep='\t')
    mask = pc['locus_group'].str.contains("protein", regex=True)
    
    protein_coding_genes = list(pc['symbol'].to_numpy()[mask])
    
    # add IG genes
    protein_coding_genes.extend(["IG_C_gene", "IG_D_gene", "IG_J_gene", "IG_LV_gene", "IG_V_gene", "TR_C_gene", "TR_J_gene", "TR_V_gene", "TR_D_gene"])
    
    return np.array(protein_coding_genes).astype("<U64")


def classify_samples(df, subtype_file):
    # load subtype file
    subtype_df = pd.read_csv(subtype_file, sep="\t")
    subtype_df.index = subtype_df['PATIENT_ID'].to_numpy()

    # replace spaces with "_"
    subtype_df['Cluster'] = subtype_df['Cluster'].str.replace(" ", "_")

    # subset to common patient IDs
    common_ids = np.intersect1d(df.columns, subtype_df['PATIENT_ID'].to_numpy())
    df = df.loc[:,common_ids]

    # add subtype row
    df.loc['subtype'] = df.columns.map(lambda x: subtype_df.loc[x, 'Cluster'])

    # print stats
    print(f'Number of common samples: {df.shape[1]} / {subtype_df.index.size}')

    return df


def pre_process_samples(expression_file, subtype_file):
    # load expression file
    df = pd.read_csv(expression_file, index_col=0)

    # set gene names as index
    df.index = df['gene_name'].to_numpy()
    df = df.iloc[:,10:]

    # subset to protein coding genes
    protein_coding_arr = protein_coding_genes_arr()
    df = df.loc[np.isin(df.index, protein_coding_arr),:]

    # remove duplicate rownames
    df = df.loc[~df.index.duplicated(),:]

    # remove control samples (controls have values >=10)
    mask = np.array(list(map(lambda x: int(re.sub("[^0-9]", "", x.split("-")[3])), df.columns.to_numpy())))
    df = df.loc[:,mask < 10]

    # subset names to patients and sort
    df.columns = list(map(lambda x: "-".join(x.split("-")[:3]), df.columns))
    df.columns = df.columns.sort_values()

    # remove duplicates
    df = df.loc[:,~df.columns.duplicated()]

    # classify samples
    df = classify_samples(df, subtype_file)

    return df.transpose()


def process_for_visuality(df, genes_oi):
    # subset to genes of interest
    df = df.loc[:,genes_oi]

    # melt
    df = df.reset_index().melt(id_vars=['index', 'subtype'], var_name='gene', value_name='expression')

    # rename gene
    df = df.rename(columns = {'index':'sample'})

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process tpm expression file
    tpm_expression_df = pre_process_samples(tpm_expression_file, subtype_file)

    # process counts expression file
    counts_expression_df = pre_process_samples(counts_expression_file, subtype_file)

    # save to files
    tpm_expression_df.to_csv(tpm_outfile, sep='\t')
    counts_expression_df.to_csv(counts_outfile, sep='\t')

    # process for visualizing
    tpm_df = process_for_visuality(tpm_expression_df, genes_oi)

    # save to file
    tpm_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"

    # genes of interest
    genes_oi = ['PLGRKT', 'METTL14', 'subtype']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tpm_expression_file=", "counts_expression_file=", "subtype_file=", "tpm_outfile=", "counts_outfile=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tpm_expression_file"):
            tpm_expression_file = str(arg)
        if opt in ("--counts_expression_file"):
            counts_expression_file = str(arg)

        if opt in ("--subtype_file"):
            subtype_file = str(arg)

        if opt in ("--tpm_outfile"):
            tpm_outfile = str(arg)
        if opt in ("--counts_outfile"):
            counts_outfile = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




