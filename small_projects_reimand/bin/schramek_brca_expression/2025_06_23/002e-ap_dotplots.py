# Alec <PERSON>

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def round_to_significant_figures(number, significant_figures):
    if number == 0:
        return 0.0
    return round(number, significant_figures - int(np.floor(np.log10(abs(number)))) - 1)


def process_df_for_dotplots(df, max_p = 300, sig_p = 0.05, fc_col = 'logFC', pval_col = 'adj.P.Val'):
    # uppercase gene, add max p-value and significance for figure
    df.loc[:,'gene'] = df.index.str.upper()

    df.loc[:,'plot_pval'] = -np.log10(df[pval_col])
    df.loc[df['plot_pval'] > max_p,'plot_pval'] = max_p

    df.loc[:,'significant'] = df[pval_col] < sig_p

    # reorder for plotting
    df = df.sort_values(['plot_pval', fc_col], ascending = [False, False])

    return df


def summarize_pathway_contributions(contributions_df, pathways_df, genes_arr, contributions_file, sig_figs=4):
    # replace column with gene name
    gene_oi = contributions_file.split('/')[-1].split('_')[0]
    contributions_df = contributions_df.rename(columns={'overlap': gene_oi})

    # define column of interest
    cols_oi = [gene_oi]

    # split and subset to genes we have measurements for
    def split_and_subset(s):
        elements = str(s).split('|')
        subset_elements = [element for element in elements if element in genes_arr]
        return subset_elements

    contributions_df.loc[:,cols_oi] = contributions_df.loc[:,cols_oi].applymap(split_and_subset)    
    
    # create a dataframe with pathway IDs and genes
    contributions_df.index = contributions_df['term_id'].to_numpy()
    contributions_df = contributions_df.loc[:,cols_oi]
    contributions_df.loc[:,'pathway'] = pathways_df['term_name'].to_numpy()
    
    # add p-value to pathway
    contributions_df.loc[:,'adjusted_p_val'] = list(map(lambda x: round_to_significant_figures(x, sig_figs), pathways_df['adjusted_p_val'].to_numpy()))
    contributions_df.loc[:,'pathway_and_pval'] = contributions_df['pathway'] + ", P=" + contributions_df['adjusted_p_val'].astype('str')
    
    
    return contributions_df, gene_oi


def prepare_figure_gene_file(gene_df, contributions_df, gene_oi):
    res_df = []

    for pathway in contributions_df['pathway']:
        # get genes in pathway
        genes_in_pathway = contributions_df.loc[contributions_df['pathway'] == pathway,gene_oi].to_numpy()[0]

        # if there are genes from this contribution
        if len(genes_in_pathway) > 0:
            # get gene information
            gene_info_df = gene_df.loc[np.isin(gene_df.index, genes_in_pathway),:].copy()
            
            # add source and pathway information
            gene_info_df.loc[:,'source'] = gene_oi
            gene_info_df.loc[:,'pathway'] = pathway

            # add to res_df
            res_df.append(gene_info_df)

    res_df = pd.concat(res_df)

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    input_df = pd.read_csv(dgea_file, sep='\t', index_col=0)
    contributions_df = pd.read_csv(contributions_details_file)
    pathways_df = pd.read_csv(pathways_outfile, sep='\t')


    # process df further and write to file
    input_df = process_df_for_dotplots(input_df)

    
    # process to get contributions from AP
    pathways_detailed_df, gene_oi = summarize_pathway_contributions(contributions_df, pathways_df, input_df['gene'].to_numpy(dtype='<U64'), contributions_details_file)
    gene_contributions_df = prepare_figure_gene_file(input_df, pathways_detailed_df, gene_oi)

    # save to files
    gene_contributions_df.to_csv(gene_contributions_file, sep='\t', index=False)

    pathways_detailed_df = pathways_detailed_df.drop(gene_oi, axis=1)
    pathways_detailed_df.to_csv(enriched_pathway_gene_contributions_file, sep='\t', index=False)

    
    # create figure
    cline = [rscript, r_script, '--enriched_pathways_dotplot_file', enriched_pathway_gene_contributions_file, '--gene_contributions_file', gene_contributions_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_file=", "contributions_details_file=", "pathways_outfile=", "gmt_infile=", "r_script=", "enriched_pathway_gene_contributions_file=", "gene_contributions_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_file"):
            dgea_file = str(arg)
        if opt in ("--contributions_details_file"):
            contributions_details_file = str(arg)
        if opt in ("--pathways_outfile"):
            pathways_outfile = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--enriched_pathway_gene_contributions_file"):
            enriched_pathway_gene_contributions_file = str(arg)
        if opt in ("--gene_contributions_file"):
            gene_contributions_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




