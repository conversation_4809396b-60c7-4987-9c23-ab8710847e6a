# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess, re

import matplotlib.cm as cm
import matplotlib as mpl

import scipy.stats as stats
import csv

help_message = '''
Failed
'''

mapped_colors_dict = {'FBXW7_q25vs25':"#eb8c2c", 'METTL14_q25vs25':'#385092', 'CSNK1A1_q25vs25':'#13864e', 'PLGRKT_q25vs25':'#d8282a', 'MRPS18B_q25vs25':'#9E1BEF'}


def read_gmt(infile):
    # read file and create a dictionary
    gmt = [line.strip("\n") for line in open(infile) if len(line.strip("\n")) > 2]
    
    ids = list(map(lambda x: x.split("\t")[0], gmt))
    other_info = list(map(lambda x: np.array(x.split("\t")[1:]), gmt))

    # dictionary of ids : names / gene arrays
    id_dict = dict(zip(ids, other_info))

    return id_dict


def write_gmt(gmt, filename):
    # write gmt to file
    with open(filename, "w") as outfile:
        for key in gmt.keys():
            outfile.write("\t".join([key, '\t'.join(gmt[key])]) + "\n")


def generate_cytoscape_file(df, mapped_colors_dict):
    # set the index to term_id
    df = df.set_index('term_id')

    # drop term_id, term_name, term_size columns
    df = df.drop(['term_name'], axis = 1)

    # create a matrix of ones and zeros depending on if the p-value exists or not
    df = (~df.isna()).astype(int)

    # add term_id back and make it the first column
    df = df.reset_index()

    # create an instruct column with the same string for every row
    instruct_str = f'piechart: attributelist="{",".join(mapped_colors_dict.keys())}" colorlist="{",".join(mapped_colors_dict.values())}" showlabels=FALSE'
    df.loc[:,'instruct'] = instruct_str

    return df


def combine_pvalues(row):
    values = row.to_numpy(dtype='float')[~row.isna()]
    return stats.combine_pvalues(values, method='fisher')[1]


def summarize_ap_results(ap_results_files_csv, mapped_colors_dict):
    files = ap_results_files_csv.split(",")
    # for each ap result file, load and summarize
    for i, file in enumerate(files):
        df = pd.read_csv(file)

        # sort columns so that 'term_id', 'term_name', 'term_size' are first; drop overlap columns
        cols = ['term_id', 'term_name', 'adjusted_p_val']
        df = df[cols]

        # rename 'adjusted_p_val' to source name
        source_name = file.split("/")[-1].split("-")[0].split(".")[0]
        source_name = re.sub("_Basal", "", source_name)

        df = df.rename(columns = {'adjusted_p_val': source_name})

        if i == 0:
            ap_df = df

        else:
            ap_df = pd.merge(ap_df, df, on = ['term_id', 'term_name'], how = 'outer')

    # generate cytoscape file
    cytoscape_df = generate_cytoscape_file(ap_df, mapped_colors_dict)

    # merge p-values
    ap_df['adjusted_p_val'] = ap_df.apply(lambda row: combine_pvalues(row[2:]), axis = 1)

    # drop source columns, keep only term_id, term_name, term_size, merged_pvalue
    ap_df = ap_df[['term_id', 'term_name', 'adjusted_p_val']]

    print(ap_df.shape)
    print(cytoscape_df.shape)

    return ap_df, cytoscape_df


def subset_gmt_file(gmt_file, ap_df):
    # load gmt file as dict
    gmt_dict = read_gmt(gmt_file)

    # subset gmt file to only include pathways in ap_df
    gmt_dict = {key: gmt_dict[key] for key in ap_df['term_id']}

    return gmt_dict


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process ap results for summarizing number of pathways
    res_df, cytoscape_df = summarize_ap_results(ap_results_files_csv, mapped_colors_dict)

    # subset gmt file to only include pathways in res_df
    gmt_dict = subset_gmt_file(original_gmt_file, res_df)

    # save results
    res_df.to_csv(combined_pathway_enrichment_file, sep='\t', index=False)
    cytoscape_df.to_csv(cytoscape_instructions_file, sep="\t", index = False, quoting=csv.QUOTE_NONE)

    # write gmt file
    write_gmt(gmt_dict, merged_gmt_file)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["ap_results_files_csv=", "original_gmt_file=", "combined_pathway_enrichment_file=", "cytoscape_instructions_file=", "merged_gmt_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--ap_results_files_csv"):
            ap_results_files_csv = str(arg)
        if opt in ("--original_gmt_file"):
            original_gmt_file = str(arg)

        if opt in ("--combined_pathway_enrichment_file"):
            combined_pathway_enrichment_file = str(arg)
        if opt in ("--cytoscape_instructions_file"):
            cytoscape_instructions_file = str(arg)
        if opt in ("--merged_gmt_file"):
            merged_gmt_file = str(arg)
            
    main()




