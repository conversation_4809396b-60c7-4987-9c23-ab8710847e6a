# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''

def load_df_for_ap(input_file, fc_cutoff = 1):
# def load_df_for_ap(input_file, fc_cutoff = 0.5849625007211562):
    # Load the file as a DataFrame
    df = pd.read_csv(input_file, sep='\t', index_col=0)

    print(df)

    # Filter the DataFrame based on the fold change cutoff
    mask = df['logFC'].abs() < fc_cutoff
    df.loc[mask,'P.Value'] = 1

    # subset to p-value
    df = df.loc[:,['P.Value']]

    return df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    df = load_df_for_ap(input_file)

    print(df)

    # save to file
    df.to_csv(ap_input_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--scores_file', ap_input_file, '--gmt_file', gmt_file, '--cytoscape_file_prefix', cytoscape_file_prefix, '--output_file', output_file, '--max_min_genes', max_min_genes]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_file=", "subtype=", "dichot_method=", "r_script=", "ap_input_file=", "gmt_file=", "cytoscape_file_prefix=", "output_file=", "max_min_genes="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_file"):
            input_file = str(arg)
        if opt in ("--subtype"):
            subtype = str(arg)
        if opt in ("--dichot_method"):
            dichot_method = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--ap_input_file"):
            ap_input_file = str(arg)
        if opt in ("--gmt_file"):
            gmt_file = str(arg)
        if opt in ("--cytoscape_file_prefix"):
            cytoscape_file_prefix = str(arg)
        if opt in ("--output_file"):
            output_file = str(arg)
        if opt in ("--max_min_genes"):
            max_min_genes = str(arg)

            
    main()




