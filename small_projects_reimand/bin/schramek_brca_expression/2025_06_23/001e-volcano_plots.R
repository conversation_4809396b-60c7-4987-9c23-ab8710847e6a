# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(grid)
library(gridExtra)
library(ggrastr)
library(ggrepel)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



pval_cutoff = 0.05
logfc_cutoff = 0.5849625007211562

volcano_plot = function(condition, main_df){

if (grepl("PLGRKT|MRPS18B", condition)){
comparison = 'upper'
}else{
comparison = 'lower'
}
    
# subset the table to the condition of interest
main_df = main_df[main_df$subtype_gene == condition,]


print(unique(main_df$dichot_method))
for (method in unique(main_df$dichot_method)){
    sub_df = main_df[main_df$dichot_method == method,]
    
# iterate through each gene
for (gene in unique(sub_df$dgea_gene)){
    input_df = sub_df[sub_df$dgea_gene == gene,]


# dynamically set ylim
max_y = ifelse(grepl("PLGRKT|MRPS18B", unique(input_df$subtype_gene)[1]), 5, 10)
input_df$log_fdr[input_df$log_fdr > max_y] = max_y
    
# separate into significant and non-significant genes
nonsig_df = input_df[input_df$fdr >= pval_cutoff,]
sig_df = input_df[input_df$fdr < pval_cutoff,]


# separate into genes past logFC logfc_cutoff and -logfc_cutoff
sigpval_df = sig_df[sig_df$logFC <= logfc_cutoff & sig_df$logFC >= -logfc_cutoff,]
sig_df = sig_df[sig_df$logFC >= logfc_cutoff | sig_df$logFC <= -logfc_cutoff,]

# separate by up and down
sig_df_up = sig_df[sig_df$logFC >= logfc_cutoff,]
sig_df_down = sig_df[sig_df$logFC <= -logfc_cutoff,]


# Calculate the counts
num_red_points = nrow(sig_df_up)
num_blue_points = nrow(sig_df_down)
# num_orange_points = nrow(sigpval_df)
# num_grey_points = nrow(input_df) - num_red_points - num_blue_points - num_orange_points
num_grey_points = nrow(input_df) - num_red_points - num_blue_points

# create plot
p = ggplot(input_df, aes(x=logFC, y=log_fdr)) + plot_theme() +

geom_vline(xintercept = -logfc_cutoff, linetype = "dashed", color = "grey") +
geom_vline(xintercept = logfc_cutoff, linetype = "dashed", color = "grey") +

geom_hline(yintercept = -log10(pval_cutoff), linetype = "dashed", color = "grey") +

rasterize(geom_point(data = sig_df_up, fill = 'firebrick', size=4, pch=21, alpha=0.8), dpi=350) + 
rasterize(geom_point(data = sig_df_down, fill = 'dodgerblue', size=4, pch=21, alpha=0.8), dpi=350) + 

# geom_point(data = sigpval_df, fill = '#f5b75c', size=4, pch=21, alpha=0.8) + 
rasterize(geom_point(data = sigpval_df, fill = 'grey', size=4, pch=21, alpha=0.2), dpi=350) + 
rasterize(geom_point(data = nonsig_df, size=4, pch=21, alpha=0.2, fill='grey'), dpi=350) + 

# ggtitle(paste(unique(input_df$subtype_gene), comparison, method, " Red:", num_red_points, "Blue:", num_blue_points, "Orange:", num_orange_points, "Grey:", num_grey_points)) +
ggtitle(paste(unique(input_df$subtype_gene), comparison, method, " Red:", num_red_points, "Blue:", num_blue_points, "Grey:", num_grey_points)) +
ylab('FDR (-log10)') + xlab('Fold change (log2)') +
ylim(0, max_y) + 

theme(axis.text.x = element_text(angle=0))

# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), 
#       axis.line = element_line(colour = "black"), 
#       plot.title = element_text(size=title_size),
#       axis.text=element_text(size=axis_size, color= 'black'), 
#       axis.text.x = element_text(angle=0),
#       axis.title=element_text(size=title_size*0.8),
#      legend.position='none')


# # genes requiring a label
# p = p +
# geom_label_repel(data=sig_df, aes(label=gene), show.legend=FALSE,
#                 seed              = 1234,
# 				size				= 3,
# 				force             = 0.5,
#                 max.overlaps      = 10,
# 				nudge_x           = 0.01,
# 				hjust             = 0,
# 				segment.size      = 0.2,
#                 color = 'black'
# )

print(p)
}
}

return()

}





pdf(opt$figure_file, height = 10, width = 12)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# subset
input_df = input_df[input_df$subtype == 'Basal' & input_df$dichot_method == 'q25vs25',]

input_df$fdr = input_df$adj.P.Val
input_df$log_fdr = -log10(input_df$fdr)

# reorder factor alphabetically
input_df$subtype_gene = factor(input_df$subtype_gene, levels = sort(levels(factor(input_df$subtype_gene))))

# create volcano plots
lapply(levels(input_df$subtype_gene), volcano_plot, input_df)


dev.off()


print(opt$figure_file)




