# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(eulerr)



# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--dgea_file"), type="character", default=NULL,
            help="",
            dest="dgea_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





# plot venn diagram
do_plot = function(data_list, condition){

venn = euler(data_list)
    
# plot
p = plot(venn, counts = TRUE, quantities = TRUE, fills = list(fill=c("darkcyan","darkorange","grey",alpha=0.2)), main=condition)

print(p)

return()
}




pdf(opt$figure_file, height = 10, width = 12)

# load df
main_df = read.csv(opt$dgea_file, sep='\t')

# subset by FDR
main_df = main_df[main_df$adj.P.Val < 0.05,]

fc_cutoff = 1
fc_cutoff2 = 0.5849625007211562



for (method in unique(main_df$dichot_method)){
    input_df = main_df[main_df$dichot_method == method,]

    print(method)
    
# subset to the right subtype and genes of interest
venn_list = list(gene1 = input_df$gene[input_df$subtype_gene == 'Basal-METTL14' & abs(input_df$logFC) > fc_cutoff],
gene2 = input_df$gene[input_df$subtype_gene == 'Basal-PLGRKT' & abs(input_df$logFC) > fc_cutoff])
venn_list2 = list(gene1 = input_df$gene[input_df$subtype_gene == 'Basal-METTL14' & abs(input_df$logFC) > fc_cutoff2],
gene2 = input_df$gene[input_df$subtype_gene == 'Basal-PLGRKT' & abs(input_df$logFC) > fc_cutoff2])

# name conditions
names(venn_list) = c('METTL14', 'PLGRKT')
names(venn_list2) = c('METTL14', 'PLGRKT')

# create a volcano plot
do_plot(venn_list, paste0('Basal ', method, "- logFC=", fc_cutoff))
do_plot(venn_list2, paste0('Basal ', method, "- logFC=", fc_cutoff2))

}


    
dev.off()


print(opt$figure_file)




