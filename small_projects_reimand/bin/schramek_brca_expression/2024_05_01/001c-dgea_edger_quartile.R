# Alec <PERSON>
library(optparse)
library(edgeR)


# options list for parser options
option_list <- list(
    make_option(c("-a","--counts_outfile"), type="character", default=NULL,
            help="",
            dest="counts_outfile"),
    make_option(c("-b","--subtype"), type="character", default=NULL,
            help="",
            dest="subtype"),
    make_option(c("-c","--gene"), type="character", default=NULL,
            help="",
            dest="gene"),
    make_option(c("-d","--dgea_file"), type="character", default=NULL,
            help="",
            dest="dgea_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




# minimum number of counts across all samples per gene
min_gene_count = 1

gene = opt$gene
subtype = opt$subtype

# load counts
rnaseq_counts = read.csv(opt$counts_outfile, sep='\t', row.names=1)


# subset to subtype
rnaseq_counts = rnaseq_counts[rnaseq_counts$subtype == subtype,]
rnaseq_counts = t(rnaseq_counts[,!(colnames(rnaseq_counts) == 'subtype')])


# Extract the values of 'thisrow'
thisrow_values <- rnaseq_counts[gene,]

# Calculate the median of 'thisrow'
split_value <- quantile(thisrow_values, na.rm = TRUE, probs = 0.75)

# Classify each column based on the median
group <- factor(ifelse(thisrow_values > split_value, "high", "low"), levels=c("low", "high"))

# cleanup df
rnaseq_counts = as.matrix(rnaseq_counts)



# Generate data object
y <- DGEList(rnaseq_counts, group = group)

# Filter for a minimum counts across all samples
keep <- filterByExpr(y, group=group, min.total.count = min_gene_count * ncol(rnaseq_counts))
y <- y[keep, , keep.lib.sizes=FALSE]

# Normalize, setup model
y <- calcNormFactors(y)
design <- model.matrix(~group)

# Apply voom normalization
v <- voom(y, design, plot = TRUE)

# Fit the linear model
fit <- lmFit(v, design)

# Correcting the makeContrasts line
cont.matrix <- makeContrasts(LowvsHigh = grouphigh, levels = design)  # No subtraction needed
fit2 <- contrasts.fit(fit, cont.matrix)
fit2 <- eBayes(fit2)

# Extract results
res <- topTable(fit2, adjust="BH", sort.by="P", n=Inf)

# save to file
write.table(as.data.frame(res), opt$dgea_file, row.names=TRUE, quote=FALSE, sep='\t')




