# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(gridExtra)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





boxplot = function(subtype, raw_input_df){


# subset to subtype
# raw_input_df = raw_input_df[raw_input_df$gene == gene,]
raw_input_df = raw_input_df[raw_input_df$subtype == subtype,]


p = ggplot(raw_input_df, aes(x = gene, y = expression)) + plot_theme() +
geom_boxplot(outlier.shape = NA, notch=FALSE) +
geom_jitter(alpha = 0.35, pch=21) + 

xlab('') + ylab("TPM") + ggtitle(subtype) +
facet_grid('.~gene', scales="free", space="free") +

theme(legend.title = element_text(size = 10), plot.title = element_text(size = 10))

print(p)

    
# repeat with log10 transformation
input_df$expression = log10(input_df$expression + 1)
    
p = ggplot(raw_input_df, aes(x = gene, y = expression)) + plot_theme() +
geom_boxplot(outlier.shape = NA, notch=FALSE) +
geom_jitter(alpha = 0.35, pch=21) + 

xlab('') + ylab("TPM (log10)") + ggtitle(subtype) +
facet_grid('.~gene', scales="free", space="free") +

theme(legend.title = element_text(size = 10), plot.title = element_text(size = 10))

print(p)


for (gene in unique(raw_input_df$gene)){

tmp_df = raw_input_df[raw_input_df$gene == gene,]
    
qqnorm(tmp_df$expression, main = gene)
qqline(tmp_df$expression)

}


return()

}




pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# create plots
lapply(unique(input_df$subtype), boxplot, input_df)



dev.off()


print(opt$figure_file)



