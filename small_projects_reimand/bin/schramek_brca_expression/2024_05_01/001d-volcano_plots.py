# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''


def load_dgea_dfs(dgea_dir):
    # list all files with regex
    files = os.path.join(dgea_dir, "001-dgea*")
    files = glob.glob(files)

    # load all files
    dfs = []

    for file in files:
        df = pd.read_csv(file, sep='\t', index_col=0)

        # make gene a column
        df['gene'] = df.index.to_numpy()

        # add details
        subtype = "_".join("-".join(file.split("/")[-1].split("-")[1:]).split("_")[1:-2])
        dgea_gene = "-".join(file.split("/")[-1].split("-")[1:]).split("_")[-2].split(".")[0]
        dichot_method = file.split("/")[-1].split("_")[-1].split(".")[0]
        
        df['subtype'] = subtype
        df['dgea_gene'] = dgea_gene
        df['dichot_method'] = dichot_method

        print(file)
        print(subtype, dgea_gene, dichot_method)

        df['subtype_gene'] = df['subtype'] + "-" + df['dgea_gene']

        dfs.append(df)

    return pd.concat(dfs)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dgea dfs from dir
    df = load_dgea_dfs(dgea_dir)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_dir=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_dir"):
            dgea_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




